import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/song.dart';
import '../models/playlist.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'audio_app.db';
  static const int _databaseVersion = 1;

  static const String _songsTable = 'songs';
  static const String _playlistsTable = 'playlists';
  static const String _playlistSongsTable = 'playlist_songs';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create songs table
    await db.execute('''
      CREATE TABLE $_songsTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        artist TEXT NOT NULL,
        album TEXT,
        filePath TEXT NOT NULL,
        duration INTEGER,
        albumArt TEXT,
        isFavorite INTEGER DEFAULT 0,
        addedAt INTEGER NOT NULL
      )
    ''');

    // Create playlists table
    await db.execute('''
      CREATE TABLE $_playlistsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        thumbnailPath TEXT
      )
    ''');

    // Create playlist_songs junction table
    await db.execute('''
      CREATE TABLE $_playlistSongsTable (
        playlistId TEXT NOT NULL,
        songId TEXT NOT NULL,
        position INTEGER NOT NULL,
        PRIMARY KEY (playlistId, songId),
        FOREIGN KEY (playlistId) REFERENCES $_playlistsTable(id) ON DELETE CASCADE,
        FOREIGN KEY (songId) REFERENCES $_songsTable(id) ON DELETE CASCADE
      )
    ''');
  }

  // Song operations
  static Future<void> insertSong(Song song) async {
    final db = await database;
    await db.insert(
      _songsTable,
      song.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<List<Song>> getAllSongs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_songsTable);
    return List.generate(maps.length, (index) => Song.fromMap(maps[index]));
  }

  static Future<Song?> getSongById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _songsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Song.fromMap(maps.first);
    }
    return null;
  }

  static Future<void> updateSong(Song song) async {
    final db = await database;
    await db.update(
      _songsTable,
      song.toMap(),
      where: 'id = ?',
      whereArgs: [song.id],
    );
  }

  static Future<void> deleteSong(String id) async {
    final db = await database;
    await db.delete(
      _songsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<List<Song>> getFavoriteSongs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _songsTable,
      where: 'isFavorite = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (index) => Song.fromMap(maps[index]));
  }

  static Future<List<Song>> searchSongs(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _songsTable,
      where: 'title LIKE ? OR artist LIKE ? OR album LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );
    return List.generate(maps.length, (index) => Song.fromMap(maps[index]));
  }

  // Playlist operations
  static Future<void> insertPlaylist(Playlist playlist) async {
    final db = await database;
    await db.insert(
      _playlistsTable,
      playlist.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<List<Playlist>> getAllPlaylists() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_playlistsTable);
    List<Playlist> playlists = [];
    
    for (var map in maps) {
      List<Song> songs = await getPlaylistSongs(map['id']);
      playlists.add(Playlist.fromMap(map).copyWith(songs: songs));
    }
    
    return playlists;
  }

  static Future<Playlist?> getPlaylistById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _playlistsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      List<Song> songs = await getPlaylistSongs(id);
      return Playlist.fromMap(maps.first).copyWith(songs: songs);
    }
    return null;
  }

  static Future<void> updatePlaylist(Playlist playlist) async {
    final db = await database;
    await db.update(
      _playlistsTable,
      playlist.toMap(),
      where: 'id = ?',
      whereArgs: [playlist.id],
    );
  }

  static Future<void> deletePlaylist(String id) async {
    final db = await database;
    await db.delete(
      _playlistsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<List<Song>> getPlaylistSongs(String playlistId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT s.* FROM $_songsTable s
      JOIN $_playlistSongsTable ps ON s.id = ps.songId
      WHERE ps.playlistId = ?
      ORDER BY ps.position
    ''', [playlistId]);
    return List.generate(maps.length, (index) => Song.fromMap(maps[index]));
  }

  static Future<void> addSongToPlaylist(String playlistId, String songId) async {
    final db = await database;
    
    // Get the next position
    final List<Map<String, dynamic>> countMaps = await db.query(
      _playlistSongsTable,
      columns: ['COUNT(*) as count'],
      where: 'playlistId = ?',
      whereArgs: [playlistId],
    );
    
    int position = countMaps.first['count'] as int;
    
    await db.insert(
      _playlistSongsTable,
      {
        'playlistId': playlistId,
        'songId': songId,
        'position': position,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<void> removeSongFromPlaylist(String playlistId, String songId) async {
    final db = await database;
    await db.delete(
      _playlistSongsTable,
      where: 'playlistId = ? AND songId = ?',
      whereArgs: [playlistId, songId],
    );
  }
} 
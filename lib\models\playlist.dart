import 'song.dart';

class Playlist {
  final String id;
  final String name;
  final String? description;
  final List<Song> songs;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? thumbnailPath;

  Playlist({
    required this.id,
    required this.name,
    this.description,
    required this.songs,
    required this.createdAt,
    required this.updatedAt,
    this.thumbnailPath,
  });

  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    List<Song>? songs,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? thumbnailPath,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      songs: songs ?? this.songs,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
    );
  }

  Duration get totalDuration {
    return songs.fold(
      Duration.zero,
      (prev, song) => prev + (song.duration ?? Duration.zero),
    );
  }

  int get songCount => songs.length;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'thumbnailPath': thumbnailPath,
    };
  }

  factory Playlist.fromMap(Map<String, dynamic> map) {
    return Playlist(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      songs: [], // Songs will be loaded separately
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      thumbnailPath: map['thumbnailPath'],
    );
  }

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, songCount: $songCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Playlist && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
} 
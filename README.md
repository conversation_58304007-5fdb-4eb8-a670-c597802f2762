# 🎵 Flutter Music Player App

A complete, fully functional audio music player app built with Flutter. This app provides a modern, intuitive interface for playing music with advanced features like playlists, search, and audio controls.

## ✨ Features

### 🎧 Core Audio Features
- **High-quality audio playback** using `just_audio` package
- **Multiple audio format support** (MP3, AAC, FLAC, WAV, etc.)
- **Real-time audio controls** (play, pause, stop, seek)
- **Volume and speed control**
- **Shuffle and repeat modes**
- **Background audio playback**

### 📱 User Interface
- **Modern Material Design 3** with dark theme
- **Bottom navigation** with three main tabs:
  - 📚 **Library**: View and manage your music collection
  - 📝 **Playlists**: Create and organize custom playlists
  - 🔍 **Search**: Find songs, artists, and albums quickly
- **Mini player** at the bottom with essential controls
- **Full-screen player** with album art and advanced controls
- **Responsive design** that works on all screen sizes

### 🎼 Music Management
- **File picker integration** to add music from device storage
- **Automatic metadata extraction** (title, artist, album)
- **Sample songs** included for immediate testing
- **Song organization** with favorites and custom playlists
- **Local storage** using SQLite database

### 🎨 Design Features
- **Beautiful animations** and smooth transitions
- **Custom gradients** and modern UI elements
- **Consistent theming** throughout the app
- **Accessibility support** with proper contrast ratios
- **User-friendly icons** and intuitive navigation

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code with Flutter extensions
- Android/iOS device or emulator

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd audio_app
   ```

2. **Install dependencies:**
   ```bash
   flutter pub get
   ```

3. **Run the app:**
   ```bash
   flutter run
   ```

## 📁 Project Structure

```
lib/
├── main.dart                 # App entry point and configuration
├── models/                   # Data models
│   ├── song.dart            # Song model with metadata
│   └── playlist.dart        # Playlist model
├── providers/               # State management
│   └── audio_provider.dart  # Audio playback state management
├── services/               # Backend services
│   └── database_service.dart # SQLite database operations
├── screens/               # Main app screens
│   └── home_screen.dart   # Main navigation screen
├── widgets/              # Reusable UI components
│   ├── library_widget.dart      # Music library interface
│   ├── playlist_widget.dart     # Playlist management
│   ├── search_widget.dart       # Search functionality
│   ├── music_player_widget.dart # Mini player controls
│   └── full_screen_player.dart  # Full player interface
```

## 🔧 Key Technologies

- **Framework**: Flutter 3.8.1
- **Audio Playback**: `just_audio` package
- **State Management**: Provider pattern
- **Local Storage**: SQLite with `sqflite`
- **File Access**: `file_picker` for music file selection
- **UI Framework**: Material Design 3

## 🎵 Usage Guide

### Adding Music
1. Open the **Library** tab
2. Tap the **"Add Music"** button
3. Select audio files from your device
4. Songs will appear in your library

### Creating Playlists
1. Go to the **Playlists** tab
2. Tap **"Create Playlist"**
3. Enter playlist name and description
4. Add songs to your playlist

### Playing Music
1. Tap any song to start playback
2. Use the mini player for basic controls
3. Tap the mini player to open full-screen mode
4. Access advanced controls like shuffle, repeat, and volume

### Searching
1. Open the **Search** tab
2. Type song title, artist, or album name
3. Tap results to play immediately

## 🛠️ Dependencies

```yaml
dependencies:
  flutter: sdk: flutter
  cupertino_icons: ^1.0.8
  
  # Audio playback
  just_audio: ^0.9.36
  audio_session: ^0.1.18
  
  # State management
  provider: ^6.1.1
  
  # File system access
  file_picker: ^6.1.1
  path_provider: ^2.1.2
  permission_handler: ^11.2.0
  
  # Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.2
  
  # UI enhancements
  cached_network_image: ^3.3.1
  palette_generator: ^0.3.3+3
  
  # Utilities
  path: ^1.8.3
```

## 🔮 Future Enhancements

- [ ] **Equalizer** with custom audio effects
- [ ] **Online streaming** support (Spotify, Apple Music APIs)
- [ ] **Lyrics display** with synchronized scrolling
- [ ] **Sleep timer** for bedtime listening
- [ ] **Smart playlists** based on listening habits
- [ ] **Social features** for sharing playlists
- [ ] **Cloud backup** for playlists and preferences
- [ ] **Car mode** with simplified interface
- [ ] **Podcasts support** with chapter navigation
- [ ] **Audio visualization** with spectrum analyzer

## 🎨 Screenshots

*Add screenshots of your app here when available*

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 💡 Support

If you encounter any issues or have questions:
1. Check the existing issues on GitHub
2. Create a new issue with detailed description
3. Provide logs and error messages if applicable

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- `just_audio` package contributors for excellent audio support
- Material Design team for beautiful design guidelines
- Open source community for inspiration and support

---

**Built with ❤️ using Flutter** 🚀

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import '../models/song.dart';

enum PlaybackState {
  stopped,
  playing,
  paused,
  loading,
}

enum RepeatMode {
  off,
  one,
  all,
}

class AudioProvider extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Current state
  Song? _currentSong;
  List<Song> _playlist = [];
  int _currentIndex = 0;
  PlaybackState _playbackState = PlaybackState.stopped;
  RepeatMode _repeatMode = RepeatMode.off;
  bool _shuffleMode = false;
  
  // Audio properties
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;

  // Getters
  Song? get currentSong => _currentSong;
  List<Song> get playlist => _playlist;
  int get currentIndex => _currentIndex;
  PlaybackState get playbackState => _playbackState;
  RepeatMode get repeatMode => _repeatMode;
  bool get shuffleMode => _shuffleMode;
  Duration get duration => _duration;
  Duration get position => _position;
  double get volume => _volume;
  bool get isPlaying => _playbackState == PlaybackState.playing;
  bool get isPaused => _playbackState == PlaybackState.paused;
  bool get hasNext => _playlist.isNotEmpty && _currentIndex < _playlist.length - 1;
  bool get hasPrevious => _playlist.isNotEmpty && _currentIndex > 0;

  AudioProvider() {
    _initialize();
  }

  void _initialize() {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      switch (state.processingState) {
        case ProcessingState.idle:
          _playbackState = PlaybackState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          _playbackState = PlaybackState.loading;
          break;
        case ProcessingState.ready:
          _playbackState = state.playing ? PlaybackState.playing : PlaybackState.paused;
          break;
        case ProcessingState.completed:
          _onSongCompleted();
          break;
      }
      notifyListeners();
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });
  }

  Future<void> loadPlaylist(List<Song> songs, {int startIndex = 0}) async {
    if (songs.isEmpty) return;
    
    _playlist = songs;
    _currentIndex = startIndex.clamp(0, songs.length - 1);
    await _loadCurrentSong();
  }

  Future<void> _loadCurrentSong() async {
    if (_playlist.isEmpty) return;
    
    final song = _playlist[_currentIndex];
    _currentSong = song;
    
    try {
      _playbackState = PlaybackState.loading;
      notifyListeners();
      
      if (File(song.filePath).existsSync()) {
        await _audioPlayer.setFilePath(song.filePath);
      } else {
        throw Exception('Audio file not found: ${song.filePath}');
      }
    } catch (e) {
      print('Error loading song: $e');
      _playbackState = PlaybackState.stopped;
      notifyListeners();
    }
  }

  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      print('Error playing: $e');
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      print('Error pausing: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      _playbackState = PlaybackState.stopped;
      notifyListeners();
    } catch (e) {
      print('Error stopping: $e');
    }
  }

  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      print('Error seeking: $e');
    }
  }

  Future<void> setVolume(double volume) async {
    try {
      _volume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_volume);
      notifyListeners();
    } catch (e) {
      print('Error setting volume: $e');
    }
  }

  Future<void> skipToNext() async {
    if (!hasNext) return;
    
    _currentIndex++;
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  Future<void> skipToPrevious() async {
    if (!hasPrevious) return;
    
    if (_position.inSeconds > 3) {
      await seek(Duration.zero);
      return;
    }
    
    _currentIndex--;
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  Future<void> skipToIndex(int index) async {
    if (index < 0 || index >= _playlist.length) return;
    
    _currentIndex = index;
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  void toggleRepeatMode() {
    switch (_repeatMode) {
      case RepeatMode.off:
        _repeatMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        _repeatMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        _repeatMode = RepeatMode.off;
        break;
    }
    notifyListeners();
  }

  void toggleShuffleMode() {
    _shuffleMode = !_shuffleMode;
    if (_shuffleMode) {
      _playlist.shuffle();
    }
    notifyListeners();
  }

  Future<void> _onSongCompleted() async {
    switch (_repeatMode) {
      case RepeatMode.one:
        await seek(Duration.zero);
        await play();
        break;
      case RepeatMode.all:
        if (hasNext) {
          await skipToNext();
        } else {
          _currentIndex = 0;
          await _loadCurrentSong();
          await play();
        }
        break;
      case RepeatMode.off:
        if (hasNext) {
          await skipToNext();
        } else {
          await stop();
        }
        break;
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
} 
import 'dart:io';
import 'package:just_audio/just_audio.dart';
import 'package:flutter/foundation.dart';
import '../models/song.dart';

enum PlaybackState {
  stopped,
  playing,
  paused,
  loading,
  buffering,
}

enum RepeatMode {
  off,
  one,
  all,
}

class AudioService extends ChangeNotifier {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Current state
  Song? _currentSong;
  List<Song> _playlist = [];
  int _currentIndex = 0;
  PlaybackState _playbackState = PlaybackState.stopped;
  RepeatMode _repeatMode = RepeatMode.off;
  bool _shuffleMode = false;
  List<int> _shuffleIndices = [];
  
  // Audio properties
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;
  double _speed = 1.0;

  // Getters
  Song? get currentSong => _currentSong;
  List<Song> get playlist => _playlist;
  int get currentIndex => _currentIndex;
  PlaybackState get playbackState => _playbackState;
  RepeatMode get repeatMode => _repeatMode;
  bool get shuffleMode => _shuffleMode;
  Duration get duration => _duration;
  Duration get position => _position;
  double get volume => _volume;
  double get speed => _speed;
  bool get isPlaying => _playbackState == PlaybackState.playing;
  bool get isPaused => _playbackState == PlaybackState.paused;
  bool get isLoading => _playbackState == PlaybackState.loading;
  bool get hasNext => _playlist.isNotEmpty && (_shuffleMode ? true : _currentIndex < _playlist.length - 1);
  bool get hasPrevious => _playlist.isNotEmpty && (_shuffleMode ? true : _currentIndex > 0);

  Future<void> initialize() async {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      switch (state.processingState) {
        case ProcessingState.idle:
          _playbackState = PlaybackState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          _playbackState = PlaybackState.loading;
          break;
        case ProcessingState.ready:
          _playbackState = state.playing ? PlaybackState.playing : PlaybackState.paused;
          break;
        case ProcessingState.completed:
          _onSongCompleted();
          break;
      }
      notifyListeners();
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });

    // Listen to volume changes
    _audioPlayer.volumeStream.listen((volume) {
      _volume = volume;
      notifyListeners();
    });

    // Listen to speed changes
    _audioPlayer.speedStream.listen((speed) {
      _speed = speed;
      notifyListeners();
    });
  }

  Future<void> loadPlaylist(List<Song> songs, {int startIndex = 0}) async {
    if (songs.isEmpty) return;
    
    _playlist = songs;
    _currentIndex = startIndex.clamp(0, songs.length - 1);
    _generateShuffleIndices();
    
    await _loadCurrentSong();
  }

  Future<void> loadSong(Song song) async {
    _playlist = [song];
    _currentIndex = 0;
    _currentSong = song;
    _generateShuffleIndices();
    
    await _loadCurrentSong();
  }

  Future<void> _loadCurrentSong() async {
    if (_playlist.isEmpty) return;
    
    final song = _playlist[_getCurrentPlayIndex()];
    _currentSong = song;
    
    try {
      _playbackState = PlaybackState.loading;
      notifyListeners();
      
      // Load the audio file
      if (File(song.filePath).existsSync()) {
        await _audioPlayer.setFilePath(song.filePath);
      } else {
        throw Exception('Audio file not found: ${song.filePath}');
      }
    } catch (e) {
      print('Error loading song: $e');
      _playbackState = PlaybackState.stopped;
      notifyListeners();
    }
  }

  int _getCurrentPlayIndex() {
    if (_shuffleMode && _shuffleIndices.isNotEmpty) {
      return _shuffleIndices[_currentIndex];
    }
    return _currentIndex;
  }

  void _generateShuffleIndices() {
    if (_playlist.isEmpty) return;
    
    _shuffleIndices = List.generate(_playlist.length, (index) => index);
    if (_shuffleMode) {
      _shuffleIndices.shuffle();
      // Make sure current song stays at current position
      final currentSongIndex = _shuffleIndices.indexOf(_currentIndex);
      if (currentSongIndex != -1 && currentSongIndex != _currentIndex) {
        _shuffleIndices[currentSongIndex] = _shuffleIndices[_currentIndex];
        _shuffleIndices[_currentIndex] = _currentIndex;
      }
    }
  }

  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      print('Error playing: $e');
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      print('Error pausing: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      _playbackState = PlaybackState.stopped;
      notifyListeners();
    } catch (e) {
      print('Error stopping: $e');
    }
  }

  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      print('Error seeking: $e');
    }
  }

  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      print('Error setting volume: $e');
    }
  }

  Future<void> setSpeed(double speed) async {
    try {
      await _audioPlayer.setSpeed(speed.clamp(0.5, 2.0));
    } catch (e) {
      print('Error setting speed: $e');
    }
  }

  Future<void> skipToNext() async {
    if (!hasNext) return;
    
    if (_shuffleMode) {
      _currentIndex = (_currentIndex + 1) % _playlist.length;
    } else {
      _currentIndex = (_currentIndex + 1).clamp(0, _playlist.length - 1);
    }
    
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  Future<void> skipToPrevious() async {
    if (!hasPrevious) return;
    
    // If we're more than 3 seconds into the song, restart current song
    if (_position.inSeconds > 3) {
      await seek(Duration.zero);
      return;
    }
    
    if (_shuffleMode) {
      _currentIndex = (_currentIndex - 1) % _playlist.length;
      if (_currentIndex < 0) _currentIndex = _playlist.length - 1;
    } else {
      _currentIndex = (_currentIndex - 1).clamp(0, _playlist.length - 1);
    }
    
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  Future<void> skipToIndex(int index) async {
    if (index < 0 || index >= _playlist.length) return;
    
    _currentIndex = index;
    await _loadCurrentSong();
    if (_playbackState == PlaybackState.playing) {
      await play();
    }
  }

  void toggleRepeatMode() {
    switch (_repeatMode) {
      case RepeatMode.off:
        _repeatMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        _repeatMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        _repeatMode = RepeatMode.off;
        break;
    }
    notifyListeners();
  }

  void toggleShuffleMode() {
    _shuffleMode = !_shuffleMode;
    _generateShuffleIndices();
    notifyListeners();
  }

  Future<void> _onSongCompleted() async {
    switch (_repeatMode) {
      case RepeatMode.one:
        await seek(Duration.zero);
        await play();
        break;
      case RepeatMode.all:
        if (hasNext) {
          await skipToNext();
        } else {
          // Go back to first song
          _currentIndex = 0;
          await _loadCurrentSong();
          await play();
        }
        break;
      case RepeatMode.off:
        if (hasNext) {
          await skipToNext();
        } else {
          await stop();
        }
        break;
    }
  }

  void addToPlaylist(Song song) {
    _playlist.add(song);
    _generateShuffleIndices();
    notifyListeners();
  }

  void removeFromPlaylist(int index) {
    if (index < 0 || index >= _playlist.length) return;
    
    if (index == _currentIndex) {
      // If removing current song, skip to next or stop
      if (_playlist.length > 1) {
        if (index == _playlist.length - 1) {
          // If it's the last song, go to previous
          _currentIndex = index - 1;
        }
        // Current index stays the same, but points to next song after removal
      } else {
        // Only one song, stop playback
        stop();
      }
    } else if (index < _currentIndex) {
      // Adjust current index if removing a song before it
      _currentIndex--;
    }
    
    _playlist.removeAt(index);
    _generateShuffleIndices();
    notifyListeners();
  }

  void clearPlaylist() {
    stop();
    _playlist.clear();
    _currentIndex = 0;
    _currentSong = null;
    _shuffleIndices.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
} 
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../providers/audio_provider.dart';
import '../models/song.dart';

class LibraryWidget extends StatefulWidget {
  const LibraryWidget({super.key});

  @override
  State<LibraryWidget> createState() => _LibraryWidgetState();
}

class _LibraryWidgetState extends State<LibraryWidget> {
  List<Song> songs = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSampleSongs();
  }

  void _loadSampleSongs() {
    songs = [
      Song(
        id: '1',
        title: 'Sample Song 1',
        artist: 'Sample Artist 1',
        album: 'Sample Album',
        filePath: '/sample/path/song1.mp3',
        addedAt: DateTime.now(),
      ),
      Song(
        id: '2',
        title: 'Sample Song 2',
        artist: 'Sample Artist 2',
        album: 'Sample Album',
        filePath: '/sample/path/song2.mp3',
        addedAt: DateTime.now(),
      ),
    ];
  }

  Future<void> _pickAudioFiles() async {
    setState(() {
      isLoading = true;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: true,
      );

      if (result != null) {
        for (var file in result.files) {
          if (file.path != null) {
            final song = Song(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              title: file.name.split('.').first,
              artist: 'Unknown Artist',
              filePath: file.path!,
              addedAt: DateTime.now(),
            );
            setState(() {
              songs.add(song);
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking files: $e')),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: isLoading ? null : _pickAudioFiles,
                  icon: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add),
                  label: Text(isLoading ? 'Loading...' : 'Add Music'),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: songs.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.library_music,
                        size: 64,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No music found',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add music files to get started',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: songs.length,
                  itemBuilder: (context, index) {
                    final song = songs[index];
                    return ListTile(
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        ),
                        child: Icon(
                          Icons.music_note,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      title: Text(
                        song.title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Text(
                        song.artist,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      trailing: PopupMenuButton<String>(
                        onSelected: (value) {
                          switch (value) {
                            case 'play':
                              Provider.of<AudioProvider>(context, listen: false)
                                  .loadPlaylist(songs, startIndex: index);
                              break;
                            case 'delete':
                              setState(() {
                                songs.removeAt(index);
                              });
                              break;
                          }
                        },
                        itemBuilder: (BuildContext context) => [
                          const PopupMenuItem<String>(
                            value: 'play',
                            child: Text('Play'),
                          ),
                          const PopupMenuItem<String>(
                            value: 'delete',
                            child: Text('Delete'),
                          ),
                        ],
                      ),
                      onTap: () {
                        Provider.of<AudioProvider>(context, listen: false)
                            .loadPlaylist(songs, startIndex: index);
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }
} 